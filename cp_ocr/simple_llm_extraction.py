"""
Simple Open-Source LLM Text Extraction from Command Window Screenshots
Uses lightweight models that work well on CPU: TrOCR and Florence-2-base
All models are open-source and run locally without API keys.
"""

import os
import sys
import warnings
import cv2
import numpy as np
from PIL import Image
import torch

# Suppress warnings
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# Input image path - CHANGE THIS TO YOUR IMAGE
image_path = r"E:\d_test\cp_ocr\cmd_images\page_1_image_1.png"

print("🤖 Simple Open-Source LLM Text Extraction")
print("=" * 55)
print(f"Processing: {os.path.basename(image_path)}")

# Check if image exists
if not os.path.exists(image_path):
    print(f"❌ Image not found: {image_path}")
    print("Available images:")
    cmd_dir = os.path.dirname(image_path)
    if os.path.exists(cmd_dir):
        for file in os.listdir(cmd_dir):
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                print(f"  - {file}")
    sys.exit(1)

def detect_device():
    """Detect available hardware for model inference."""
    try:
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            print(f"🔧 GPU detected: {gpu_name}")
            return 'cuda'
        else:
            print("🔧 Using CPU (GPU not available)")
            return 'cpu'
    except:
        print("🔧 Using CPU (PyTorch not available)")
        return 'cpu'

device = detect_device()

def preprocess_image(image_path):
    """Enhanced preprocessing for vision-language models."""
    print("🔧 Preprocessing image...")
    
    # Load image
    image = cv2.imread(image_path)
    original_pil = Image.open(image_path).convert('RGB')
    
    # Convert to grayscale for analysis
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    height, width = gray.shape
    print(f"   Image size: {width}x{height}")
    
    # Detect command window region
    dark_start_row = None
    for row in range(0, height - 10):
        avg_brightness = np.mean(gray[row:row+10, :])
        if avg_brightness < 60:
            dark_start_row = row
            break
    
    if dark_start_row is not None:
        print(f"   ✅ Command window detected at row {dark_start_row}")
        
        # Extract terminal region
        terminal_region = image[dark_start_row:, :]
        terminal_gray = cv2.cvtColor(terminal_region, cv2.COLOR_BGR2GRAY)
        
        # Create enhanced version for OCR
        inverted = cv2.bitwise_not(terminal_gray)
        resized = cv2.resize(inverted, None, fx=1.5, fy=1.5, interpolation=cv2.INTER_CUBIC)
        enhanced_pil = Image.fromarray(resized).convert('RGB')
        
        return original_pil, enhanced_pil, True
    else:
        print(f"   ⚠️ No command window detected (avg brightness: {np.mean(gray):.1f})")
        return original_pil, original_pil, False

# Preprocess the image
original_image, enhanced_image, is_terminal = preprocess_image(image_path)

# Store all results
llm_results = {}

print("\n" + "=" * 55)
print("EXTRACTING TEXT WITH LIGHTWEIGHT LLMs")
print("=" * 55)

# METHOD 1: TrOCR (Transformer-based OCR)
print("\n1. 🤖 TrOCR (Transformer-based OCR)")
print("-" * 40)

try:
    print("   📥 Loading TrOCR model...")
    from transformers import TrOCRProcessor, VisionEncoderDecoderModel
    
    # Use base model for better CPU performance
    model_name = 'microsoft/trocr-base-printed'
    
    processor = TrOCRProcessor.from_pretrained(model_name)
    model = VisionEncoderDecoderModel.from_pretrained(model_name)
    
    # Move to device
    model = model.to(device)
    print(f"      ✅ TrOCR loaded on {device}")
    
    # Use enhanced image for better OCR
    image_to_use = enhanced_image if is_terminal else original_image
    
    # Process image
    pixel_values = processor(image_to_use, return_tensors="pt").pixel_values.to(device)
    
    print("      🔄 Generating text extraction...")
    with torch.no_grad():
        generated_ids = model.generate(pixel_values, max_length=1000, num_beams=4, early_stopping=True)
    
    trocr_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
    
    if trocr_text.strip():
        llm_results['trocr'] = trocr_text
        print(f"      ✅ TrOCR Success: {len(trocr_text)} characters")
    else:
        llm_results['trocr'] = ""
        print("      ⚠️ TrOCR: No text extracted")
        
except ImportError:
    llm_results['trocr'] = ""
    print("      ❌ TrOCR not installed. Run: pip install transformers torch")
except Exception as e:
    llm_results['trocr'] = ""
    print(f"      ❌ TrOCR error: {str(e)}")

# METHOD 2: Florence-2-base (Lighter version)
print("\n2. 🏛️ Florence-2-base (Microsoft Vision Model)")
print("-" * 40)

try:
    print("   📥 Loading Florence-2-base model...")
    from transformers import AutoProcessor, AutoModelForCausalLM
    
    # Use base model for better performance
    model_name = "microsoft/Florence-2-base"
    
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float32,  # Use float32 for CPU compatibility
        trust_remote_code=True
    ).to(device)
    
    processor = AutoProcessor.from_pretrained(model_name, trust_remote_code=True)
    
    print(f"      ✅ Florence-2-base loaded on {device}")
    
    # Florence-2 uses specific task prompts
    prompt = "<OCR>"
    
    # Use enhanced image for better OCR
    image_to_use = enhanced_image if is_terminal else original_image
    
    inputs = processor(text=prompt, images=image_to_use, return_tensors="pt").to(device)
    
    print("      🔄 Generating text extraction...")
    with torch.no_grad():
        generated_ids = model.generate(
            input_ids=inputs["input_ids"],
            pixel_values=inputs["pixel_values"],
            max_new_tokens=512,
            num_beams=3,
            do_sample=False
        )
    
    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
    
    # Parse Florence-2 output
    try:
        parsed_answer = processor.post_process_generation(
            generated_text, 
            task="<OCR>", 
            image_size=(image_to_use.width, image_to_use.height)
        )
        
        # Extract text from parsed answer
        extracted_text = ""
        if '<OCR>' in parsed_answer:
            extracted_text = parsed_answer['<OCR>']
        
        if extracted_text.strip():
            llm_results['florence2'] = extracted_text
            print(f"      ✅ Florence-2 Success: {len(extracted_text)} characters")
        else:
            llm_results['florence2'] = ""
            print("      ⚠️ Florence-2: No text extracted")
    except Exception as parse_error:
        # Fallback: try to extract text directly from generated text
        if "<OCR>" in generated_text and "</OCR>" in generated_text:
            start = generated_text.find("<OCR>") + 5
            end = generated_text.find("</OCR>")
            extracted_text = generated_text[start:end].strip()
            
            if extracted_text:
                llm_results['florence2'] = extracted_text
                print(f"      ✅ Florence-2 Success (fallback): {len(extracted_text)} characters")
            else:
                llm_results['florence2'] = ""
                print("      ⚠️ Florence-2: No text extracted (fallback)")
        else:
            llm_results['florence2'] = ""
            print(f"      ⚠️ Florence-2 parsing error: {str(parse_error)}")
        
except ImportError:
    llm_results['florence2'] = ""
    print("      ❌ Florence-2 not installed. Run: pip install transformers torch")
except Exception as e:
    llm_results['florence2'] = ""
    print(f"      ❌ Florence-2 error: {str(e)}")

print("\n" + "=" * 55)
print("LIGHTWEIGHT LLM EXTRACTION RESULTS")
print("=" * 55)

# Display results from each LLM
successful_extractions = []

for model_name, text in llm_results.items():
    print(f"\n[{model_name.upper()}]")
    print("-" * 30)
    
    if text and text.strip():
        successful_extractions.append((model_name, text))
        print(f"✅ Success: {len(text)} characters extracted")
        
        # Show preview
        preview = text[:200] + "..." if len(text) > 200 else text
        print("Preview:")
        print(preview)
    else:
        print("❌ No text extracted")

# Determine best result
print("\n" + "=" * 55)
print("BEST LIGHTWEIGHT LLM RESULT")
print("=" * 55)

if successful_extractions:
    # Choose the result with more text (simple heuristic)
    best_result = max(successful_extractions, key=lambda x: len(x[1]))
    model_name, best_text = best_result
    
    print(f"🏆 Best extraction: {model_name.upper()}")
    print(f"📊 Text length: {len(best_text)} characters")
    print(f"🔧 Command window detected: {is_terminal}")
    print(f"💻 Device used: {device}")
    print("\n" + "🎯 EXTRACTED TEXT:")
    print("=" * 40)
    print(best_text)
    
    # Save to file
    output_file = f"simple_llm_extracted_{os.path.splitext(os.path.basename(image_path))[0]}.txt"
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"Simple LLM Text Extraction Results\n")
            f.write(f"Image: {os.path.basename(image_path)}\n")
            f.write(f"Best Model: {model_name.upper()}\n")
            f.write(f"Command Window Detected: {is_terminal}\n")
            f.write(f"Device Used: {device}\n")
            f.write("=" * 50 + "\n\n")
            f.write(best_text)
            f.write("\n\n" + "=" * 50 + "\n\n")
            
            # Add all results
            f.write("ALL RESULTS:\n")
            f.write("-" * 20 + "\n\n")
            for model, text in llm_results.items():
                f.write(f"[{model.upper()}]\n")
                if text and text.strip():
                    f.write(text)
                else:
                    f.write("No text extracted")
                f.write("\n\n")
        
        print(f"\n💾 Results saved to: {output_file}")
        
    except Exception as e:
        print(f"\n⚠️ Could not save to file: {str(e)}")
else:
    print("❌ No text extracted by any lightweight LLM")
    print("\n🔧 Troubleshooting:")
    print("1. Models are downloading on first use (may take time)")
    print("2. Ensure stable internet connection for model download")
    print("3. Try with a clearer command window screenshot")

# Cleanup GPU memory
if device == 'cuda':
    try:
        torch.cuda.empty_cache()
        print("\n🧹 GPU memory cleared")
    except:
        pass

print("\n" + "=" * 55)
print("✅ SIMPLE LLM TEXT EXTRACTION COMPLETED")
print("=" * 55)

total_methods = len(llm_results)
successful_methods = len(successful_extractions)
print(f"📊 Summary: {successful_methods}/{total_methods} lightweight LLMs successful")

if successful_extractions:
    print(f"🎯 Best result: {len(best_result[1])} characters from {best_result[0].upper()}")

print(f"💻 Hardware used: {device}")
print("💡 To process a different image, change the 'image_path' variable at the top.")
print("🚀 These lightweight models work well on CPU and require less memory.")
