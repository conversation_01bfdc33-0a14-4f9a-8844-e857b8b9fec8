from transformers import VisionEncoderDecoderModel, DonutProcessor
from PIL import Image
import torch

# Load the processor and model from HuggingFace
processor = DonutProcessor.from_pretrained("naver-clova-ix/donut-base", use_fast=True)
model = VisionEncoderDecoderModel.from_pretrained("naver-clova-ix/donut-base")

# Set the model to evaluation mode
model.eval()

# Load your image
image_path = "cmd_images/page_1_image_2.png"
image = Image.open(image_path).convert("RGB")

# Preprocess image and prepare decoder input
task_prompt = "<s_docvqa><s_question>What is written in the terminal?<s_answer>"
pixel_values = processor(image, return_tensors="pt").pixel_values

# Generate output using model
with torch.no_grad():
    generated_ids = model.generate(
        pixel_values,
        decoder_input_ids=processor.tokenizer(task_prompt, return_tensors="pt").input_ids,
        max_length=512,
        early_stopping=True,
        pad_token_id=processor.tokenizer.pad_token_id,
        eos_token_id=processor.tokenizer.eos_token_id,
    )

# Decode the generated tokens to text
output = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
print("📝 Extracted Text:\n", output)
