"""
Advanced Open-Source LLM Text Extraction from Command Window Screenshots
Uses state-of-the-art vision-language models: LLaVA, Qwen2-VL, Florence-2
All models are open-source and run locally without API keys.
"""

import os
import sys
import warnings
import cv2
import numpy as np
from PIL import Image
import torch

# Suppress warnings
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=UserWarning)

# Input image path - CHANGE THIS TO YOUR IMAGE
image_path = r"E:\d_test\cp_ocr\cmd_images\page_1_image_2.png"

print("🤖 Advanced Open-Source LLM Text Extraction")
print("=" * 60)
print(f"Processing: {os.path.basename(image_path)}")

# Check if image exists
if not os.path.exists(image_path):
    print(f"❌ Image not found: {image_path}")
    print("Available images:")
    cmd_dir = os.path.dirname(image_path)
    if os.path.exists(cmd_dir):
        for file in os.listdir(cmd_dir):
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                print(f"  - {file}")
    sys.exit(1)

def detect_device():
    """Detect available hardware for model inference."""
    try:
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            print(f"🔧 GPU detected: {gpu_name} ({gpu_memory:.1f}GB)")
            return 'cuda'
        else:
            print("🔧 Using CPU (GPU not available)")
            return 'cpu'
    except:
        print("🔧 Using CPU (PyTorch not available)")
        return 'cpu'

device = detect_device()

def preprocess_image(image_path):
    """Enhanced preprocessing for vision-language models."""
    print("🔧 Preprocessing image for LLM models...")
    
    # Load image
    image = cv2.imread(image_path)
    original_pil = Image.open(image_path).convert('RGB')
    
    # Convert to grayscale for analysis
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    height, width = gray.shape
    print(f"   Image size: {width}x{height}")
    
    # Detect command window region
    dark_start_row = None
    for row in range(0, height - 10):
        avg_brightness = np.mean(gray[row:row+10, :])
        if avg_brightness < 60:
            dark_start_row = row
            break
    
    if dark_start_row is not None:
        print(f"   ✅ Command window detected at row {dark_start_row}")
        
        # For LLMs, we often want to keep the original image but can also provide enhanced version
        terminal_region = image[dark_start_row:, :]
        terminal_pil = Image.fromarray(cv2.cvtColor(terminal_region, cv2.COLOR_BGR2RGB))
        
        # Create enhanced version
        terminal_gray = cv2.cvtColor(terminal_region, cv2.COLOR_BGR2GRAY)
        inverted = cv2.bitwise_not(terminal_gray)
        enhanced_pil = Image.fromarray(inverted).convert('RGB')
        
        return original_pil, terminal_pil, enhanced_pil, True
    else:
        print(f"   ⚠️ No command window detected (avg brightness: {np.mean(gray):.1f})")
        return original_pil, original_pil, original_pil, False

# Preprocess the image
original_image, terminal_image, enhanced_image, is_terminal = preprocess_image(image_path)

# Store all results
llm_results = {}

print("\n" + "=" * 60)
print("EXTRACTING TEXT WITH OPEN-SOURCE LLMs")
print("=" * 60)

# METHOD 1: LLaVA (Large Language and Vision Assistant)
print("\n1. 🦙 LLaVA (Large Language and Vision Assistant)")
print("-" * 50)

try:
    print("   📥 Loading LLaVA model...")
    from transformers import LlavaNextProcessor, LlavaNextForConditionalGeneration
    
    # Use a smaller LLaVA model for better compatibility
    model_name = "llava-hf/llava-1.5-7b-hf"
    
    processor = LlavaNextProcessor.from_pretrained(model_name)
    model = LlavaNextForConditionalGeneration.from_pretrained(
        model_name,
        torch_dtype=torch.float16 if device == 'cuda' else torch.float32,
        device_map="auto" if device == 'cuda' else None,
        low_cpu_mem_usage=True
    )
    
    if device == 'cpu':
        model = model.to('cpu')
    
    print(f"      ✅ LLaVA loaded on {device}")
    
    # Prepare prompt for command window text extraction
    prompt = """Extract all text from this command window screenshot. Focus on:
1. Commands that were executed (after prompts like [root@...] or C:\>)
2. Output and results from those commands
3. Error messages if any
4. Maintain the original formatting and structure

Please provide the extracted text in a clean, readable format while preserving the command-output relationship."""
    
    # Use the best image version
    image_to_use = terminal_image if is_terminal else original_image
    
    # Process inputs
    inputs = processor(prompt, image_to_use, return_tensors="pt")
    if device == 'cuda':
        inputs = {k: v.to(device) for k, v in inputs.items()}
    
    # Generate response
    print("      🔄 Generating text extraction...")
    with torch.no_grad():
        output = model.generate(
            **inputs,
            max_new_tokens=2000,
            do_sample=False,
            temperature=0.1,
            pad_token_id=processor.tokenizer.eos_token_id
        )
    
    # Decode the response
    generated_text = processor.decode(output[0], skip_special_tokens=True)
    
    # Extract only the generated part (remove the prompt)
    if prompt in generated_text:
        extracted_text = generated_text.split(prompt)[-1].strip()
    else:
        extracted_text = generated_text.strip()
    
    if extracted_text:
        llm_results['llava'] = extracted_text
        print(f"      ✅ LLaVA Success: {len(extracted_text)} characters")
    else:
        llm_results['llava'] = ""
        print("      ⚠️ LLaVA: No text extracted")
        
except ImportError:
    llm_results['llava'] = ""
    print("      ❌ LLaVA not installed. Run: pip install transformers torch")
except Exception as e:
    llm_results['llava'] = ""
    print(f"      ❌ LLaVA error: {str(e)}")

# METHOD 2: Qwen2-VL (State-of-the-art Vision-Language Model)
print("\n2. 🔮 Qwen2-VL (Advanced Vision-Language Model)")
print("-" * 50)

try:
    print("   📥 Loading Qwen2-VL model...")
    from transformers import Qwen2VLForConditionalGeneration, AutoTokenizer, AutoProcessor
    
    # Use smaller Qwen2-VL model
    model_name = "Qwen/Qwen2-VL-2B-Instruct"
    
    model = Qwen2VLForConditionalGeneration.from_pretrained(
        model_name,
        torch_dtype=torch.bfloat16 if device == 'cuda' else torch.float32,
        device_map="auto" if device == 'cuda' else None,
        attn_implementation="flash_attention_2" if device == 'cuda' else "eager"
    )
    
    processor = AutoProcessor.from_pretrained(model_name)
    
    if device == 'cpu':
        model = model.to('cpu')
    
    print(f"      ✅ Qwen2-VL loaded on {device}")
    
    # Prepare messages for Qwen2-VL
    image_to_use = terminal_image if is_terminal else original_image
    
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": image_to_use,
                },
                {
                    "type": "text", 
                    "text": "Extract all text from this command window screenshot. Focus on commands and their outputs. Maintain original formatting and structure."
                },
            ],
        }
    ]
    
    # Prepare inputs
    text = processor.apply_chat_template(
        messages, tokenize=False, add_generation_prompt=True
    )
    
    # Process vision info
    image_inputs = [image_to_use]
    video_inputs = []
    
    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
    )
    
    if device == 'cuda':
        inputs = inputs.to(device)
    
    # Generate
    print("      🔄 Generating text extraction...")
    with torch.no_grad():
        generated_ids = model.generate(**inputs, max_new_tokens=2000, temperature=0.1)
    
    generated_ids_trimmed = [
        out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]
    
    output_text = processor.batch_decode(
        generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
    )[0]
    
    if output_text.strip():
        llm_results['qwen2_vl'] = output_text.strip()
        print(f"      ✅ Qwen2-VL Success: {len(output_text)} characters")
    else:
        llm_results['qwen2_vl'] = ""
        print("      ⚠️ Qwen2-VL: No text extracted")
        
except ImportError:
    llm_results['qwen2_vl'] = ""
    print("      ❌ Qwen2-VL not installed. Run: pip install transformers torch")
except Exception as e:
    llm_results['qwen2_vl'] = ""
    print(f"      ❌ Qwen2-VL error: {str(e)}")

# METHOD 3: Florence-2 (Microsoft's Vision Foundation Model)
print("\n3. 🏛️ Florence-2 (Microsoft Vision Foundation Model)")
print("-" * 50)

try:
    print("   📥 Loading Florence-2 model...")
    from transformers import AutoProcessor, AutoModelForCausalLM
    
    model_name = "microsoft/Florence-2-large"
    
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16 if device == 'cuda' else torch.float32,
        trust_remote_code=True
    ).to(device)
    
    processor = AutoProcessor.from_pretrained(model_name, trust_remote_code=True)
    
    print(f"      ✅ Florence-2 loaded on {device}")
    
    # Florence-2 uses specific task prompts
    prompt = "<OCR_WITH_REGION>"  # More detailed OCR task
    
    # Use enhanced image for better OCR
    image_to_use = enhanced_image if is_terminal else original_image
    
    inputs = processor(text=prompt, images=image_to_use, return_tensors="pt").to(device)
    
    print("      🔄 Generating text extraction...")
    with torch.no_grad():
        generated_ids = model.generate(
            input_ids=inputs["input_ids"],
            pixel_values=inputs["pixel_values"],
            max_new_tokens=1024,
            num_beams=3,
            do_sample=False
        )
    
    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
    
    # Parse Florence-2 output
    parsed_answer = processor.post_process_generation(
        generated_text, 
        task="<OCR_WITH_REGION>", 
        image_size=(image_to_use.width, image_to_use.height)
    )
    
    # Extract text from parsed answer
    extracted_text = ""
    if '<OCR_WITH_REGION>' in parsed_answer:
        ocr_result = parsed_answer['<OCR_WITH_REGION>']
        if 'text' in ocr_result:
            extracted_text = ' '.join(ocr_result['text'])
        elif isinstance(ocr_result, str):
            extracted_text = ocr_result
    
    if extracted_text.strip():
        llm_results['florence2'] = extracted_text
        print(f"      ✅ Florence-2 Success: {len(extracted_text)} characters")
    else:
        llm_results['florence2'] = ""
        print("      ⚠️ Florence-2: No text extracted")
        
except ImportError:
    llm_results['florence2'] = ""
    print("      ❌ Florence-2 not installed. Run: pip install transformers torch")
except Exception as e:
    llm_results['florence2'] = ""
    print(f"      ❌ Florence-2 error: {str(e)}")

print("\n" + "=" * 60)
print("LLM EXTRACTION RESULTS")
print("=" * 60)

# Display results from each LLM
successful_llm_extractions = []

for model_name, text in llm_results.items():
    print(f"\n[{model_name.upper()}]")
    print("-" * 40)

    if text and text.strip():
        successful_llm_extractions.append((model_name, text))
        print(f"✅ Success: {len(text)} characters extracted")

        # Show preview (first 300 characters for LLMs as they tend to be more verbose)
        preview = text[:300] + "..." if len(text) > 300 else text
        print("Preview:")
        print(preview)
    else:
        print("❌ No text extracted")

# Determine best LLM result
print("\n" + "=" * 60)
print("BEST LLM RESULT")
print("=" * 60)

if successful_llm_extractions:
    # Prioritize based on model capability and text quality
    model_priority = ['qwen2_vl', 'llava', 'florence2']
    best_llm_result = None
    best_score = 0

    for model_name, text in successful_llm_extractions:
        text_length = len(text.strip())
        if text_length == 0:
            continue

        # Calculate score based on text length and model priority
        priority_score = len(model_priority) - model_priority.index(model_name) if model_name in model_priority else 1
        score = text_length * priority_score

        if score > best_score:
            best_score = score
            best_llm_result = (model_name, text)

    if best_llm_result:
        model_name, best_text = best_llm_result
        print(f"🏆 Best LLM extraction: {model_name.upper()}")
        print(f"📊 Text length: {len(best_text)} characters")
        print(f"🔧 Command window detected: {is_terminal}")
        print(f"💻 Device used: {device}")
        print("\n" + "🎯 EXTRACTED TEXT:")
        print("=" * 50)
        print(best_text)

        # Save to file
        output_file = f"llm_extracted_text_{os.path.splitext(os.path.basename(image_path))[0]}.txt"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"Advanced LLM Text Extraction Results\n")
                f.write(f"Image: {os.path.basename(image_path)}\n")
                f.write(f"Best LLM Model: {model_name.upper()}\n")
                f.write(f"Command Window Detected: {is_terminal}\n")
                f.write(f"Device Used: {device}\n")
                f.write("=" * 60 + "\n\n")
                f.write("BEST EXTRACTION:\n")
                f.write("-" * 20 + "\n")
                f.write(best_text)
                f.write("\n\n" + "=" * 60 + "\n\n")

                # Add all LLM results for comparison
                f.write("ALL LLM EXTRACTION RESULTS:\n")
                f.write("=" * 40 + "\n\n")
                for model, text in llm_results.items():
                    f.write(f"[{model.upper()}]\n")
                    f.write("-" * 30 + "\n")
                    if text and text.strip():
                        f.write(text)
                    else:
                        f.write("No text extracted")
                    f.write("\n\n")

            print(f"\n💾 LLM results saved to: {output_file}")

        except Exception as e:
            print(f"\n⚠️ Could not save to file: {str(e)}")
    else:
        print("❌ No successful text extraction from any LLM")
else:
    print("❌ No text extracted by any LLM model")
    print("\n🔧 Troubleshooting suggestions:")
    print("1. Install required dependencies:")
    print("   pip install transformers torch torchvision")
    print("2. For GPU acceleration (recommended):")
    print("   pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118")
    print("3. Ensure sufficient memory (LLMs require 4-8GB RAM/VRAM)")
    print("4. Try with a clearer command window screenshot")
    print("5. Check if the image contains readable text")

# Compare with traditional OCR if available
print("\n" + "=" * 60)
print("QUICK TRADITIONAL OCR COMPARISON")
print("=" * 60)

try:
    import easyocr

    print("🔤 Running EasyOCR for comparison...")
    reader = easyocr.Reader(['en'], gpu=(device == 'cuda'))

    # Use enhanced image
    image_to_use = enhanced_image if is_terminal else original_image
    img_array = np.array(image_to_use)

    ocr_result = reader.readtext(img_array, detail=0)
    ocr_text = '\n'.join(ocr_result)

    if ocr_text.strip():
        print(f"✅ Traditional OCR: {len(ocr_text)} characters")

        # Compare with best LLM result
        if successful_llm_extractions and best_llm_result:
            llm_length = len(best_llm_result[1])
            ocr_length = len(ocr_text)

            print(f"\n📊 Comparison:")
            print(f"   Best LLM ({best_llm_result[0]}): {llm_length} characters")
            print(f"   Traditional OCR: {ocr_length} characters")

            if llm_length > ocr_length * 1.2:
                print("   🎯 LLM extracted significantly more text")
            elif ocr_length > llm_length * 1.2:
                print("   🎯 Traditional OCR extracted more text")
            else:
                print("   🎯 Both methods extracted similar amounts")
    else:
        print("⚠️ Traditional OCR: No text extracted")

except ImportError:
    print("⚠️ EasyOCR not available for comparison")
except Exception as e:
    print(f"⚠️ OCR comparison error: {str(e)}")

# Cleanup GPU memory
if device == 'cuda':
    try:
        torch.cuda.empty_cache()
        print("\n🧹 GPU memory cleared")
    except:
        pass

print("\n" + "=" * 60)
print("✅ ADVANCED LLM TEXT EXTRACTION COMPLETED")
print("=" * 60)

# Summary statistics
total_llm_methods = len(llm_results)
successful_llm_methods = len(successful_llm_extractions)
print(f"📊 LLM Summary: {successful_llm_methods}/{total_llm_methods} models successful")

if successful_llm_extractions:
    print(f"🎯 Best LLM result: {len(best_llm_result[1])} characters from {best_llm_result[0].upper()}")
    print(f"📁 Results saved to: llm_extracted_text_{os.path.splitext(os.path.basename(image_path))[0]}.txt")

print(f"\n💻 Hardware used: {device}")
print("💡 To process a different image, change the 'image_path' variable at the top of this file.")
print("🚀 For better performance, use a GPU with at least 8GB VRAM.")
